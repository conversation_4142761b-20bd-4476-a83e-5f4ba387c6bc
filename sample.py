from flask import Flask, request, render_template, Response, redirect, url_for
import mysql.connector

app = Flask(__name__)

# Allow 256MB file upload
app.config['MAX_CONTENT_LENGTH'] = 256 * 1024 * 1024  # 256 MB

# Database configuration
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
}

# Home route: Show upload form and videos
@app.route('/')
def index():
    try:
        db = mysql.connector.connect(**db_config)
        cursor = db.cursor()

        # Create table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS videos (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL,
                video_data LONGBLOB NOT NULL,
                uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        cursor.execute("SELECT id, filename FROM videos ORDER BY uploaded_at DESC")
        videos = cursor.fetchall()
        cursor.close()
        db.close()
        return render_template('index.html', videos=videos)
    except Exception as e:
        return f"Database error: {str(e)}", 500

# Upload handler (supports both GET and POST)
@app.route('/upload', methods=['GET', 'POST'])
def upload():
    if request.method == 'GET':
        # Show upload form
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Video Upload</title>
            <style>
                body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
                .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
                button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
                button:hover { background: #0056b3; }
                .status { margin: 20px 0; padding: 10px; border-radius: 4px; }
                .success { background: #d4edda; color: #155724; }
                .error { background: #f8d7da; color: #721c24; }
            </style>
        </head>
        <body>
            <h1>Video Upload Test</h1>
            <div class="upload-area">
                <form action="/upload" method="post" enctype="multipart/form-data">
                    <input type="file" name="video" accept="video/*" required>
                    <br><br>
                    <button type="submit">Upload Video</button>
                </form>
            </div>
            <p><a href="/">← Back to Video List</a></p>
        </body>
        </html>
        '''

    # Handle POST request (file upload)
    file = request.files.get('video')
    if not file or not file.filename:
        return "No file selected", 400

    # Optional: validate type
    if not file.filename.endswith(('.mp4', '.webm', '.mov', '.avi')):
        return "Invalid file type. Please upload MP4, WebM, MOV, or AVI files.", 400

    try:
        video_data = file.read()
        db = mysql.connector.connect(**db_config)
        cursor = db.cursor()

        # Create table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS videos (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL,
                video_data LONGBLOB NOT NULL,
                uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        cursor.execute("INSERT INTO videos (filename, video_data) VALUES (%s, %s)", (file.filename, video_data))
        db.commit()
        cursor.close()
        db.close()

        return redirect(url_for('index'))

    except Exception as e:
        return f"Upload failed: {str(e)}", 500

# Video streaming
@app.route('/video/<int:video_id>')
def stream_video(video_id):
    try:
        db = mysql.connector.connect(**db_config)
        cursor = db.cursor()
        cursor.execute("SELECT video_data, filename FROM videos WHERE id = %s", (video_id,))
        row = cursor.fetchone()
        cursor.close()
        db.close()

        if row and row[0]:
            # Get file extension to determine proper mimetype
            filename = row[1] if len(row) > 1 else 'video.mp4'
            if filename.endswith('.webm'):
                mimetype = 'video/webm'
            elif filename.endswith('.mov'):
                mimetype = 'video/quicktime'
            elif filename.endswith('.avi'):
                mimetype = 'video/x-msvideo'
            else:
                mimetype = 'video/mp4'

            response = Response(row[0], mimetype=mimetype)
            response.headers['Content-Disposition'] = f'inline; filename="{filename}"'
            return response
        return "Video not found", 404
    except Exception as e:
        return f"Error streaming video: {str(e)}", 500

if __name__ == '__main__':
    app.run(debug=True)
