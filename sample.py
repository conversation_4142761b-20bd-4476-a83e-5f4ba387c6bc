from flask import Flask, request, render_template, Response, redirect, url_for
import mysql.connector

app = Flask(__name__)

# Allow 256MB file upload
app.config['MAX_CONTENT_LENGTH'] = 256 * 1024 * 1024  # 256 MB

# Database configuration
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
}

# Home route: Show upload form and videos
@app.route('/')
def index():
    db = mysql.connector.connect(**db_config)
    cursor = db.cursor()
    cursor.execute("SELECT id, filename FROM videos ORDER BY uploaded_at DESC")
    videos = cursor.fetchall()
    cursor.close()
    db.close()
    return render_template('index.html', videos=videos)

# Upload handler (AJAX)
@app.route('/upload', methods=['POST'])
def upload():
    file = request.files.get('video')
    if not file or not file.filename:
        return "No file selected", 400

    # Optional: validate type
    if not file.filename.endswith(('.mp4', '.webm', '.mov')):
        return "Invalid file type", 400

    video_data = file.read()
    db = mysql.connector.connect(**db_config)
    cursor = db.cursor()
    cursor.execute("INSERT INTO videos (filename, video_data) VALUES (%s, %s)", (file.filename, video_data))
    db.commit()
    cursor.close()
    db.close()
    return "Upload successful", 200

# Video streaming
@app.route('/video/<int:video_id>')
def stream_video(video_id):
    db = mysql.connector.connect(**db_config)
    cursor = db.cursor()
    cursor.execute("SELECT video_data FROM videos WHERE id = %s", (video_id,))
    row = cursor.fetchone()
    cursor.close()
    db.close()

    if row:
        return Response(row[0], mimetype='video/mp4')
    return "Video not found", 404

if __name__ == '__main__':
    app.run(debug=True)
