<!DOCTYPE html>
<html>
<head>
    <title>Video Upload</title>
</head>
<body>
    <h1>Upload a Video</h1>
    <input type="file" id="videoInput">
    <button onclick="uploadVideo()">Upload</button>
    <br><br>
    <progress id="progressBar" value="0" max="100" style="width: 100%;"></progress>
    <br><br>

    <h2>Uploaded Videos</h2>
    {% for video in videos %}
        <div>
            <p>{{ video[1] }}</p>
            <video width="320" height="240" controls>
                <source src="{{ url_for('stream_video', video_id=video[0]) }}" type="video/mp4">
            </video>
        </div>
    {% endfor %}

    <script>
        function uploadVideo() {
            const fileInput = document.getElementById('videoInput');
            const file = fileInput.files[0];

            if (!file) {
                alert("Please select a file.");
                return;
            }

            const xhr = new XMLHttpRequest();
            xhr.open("POST", "/upload", true);

            xhr.upload.onprogress = function (event) {
                if (event.lengthComputable) {
                    const percent = (event.loaded / event.total) * 100;
                    document.getElementById("progressBar").value = percent;
                }
            };

            xhr.onload = function () {
                if (xhr.status === 200) {
                    alert("Upload complete!");
                    window.location.reload();
                } else {
                    alert("Upload failed: " + xhr.responseText);
                }
            };

            const formData = new FormData();
            formData.append("video", file);
            xhr.send(formData);
        }
    </script>
</body>
</html>
