<!DOCTYPE html>
<html>
<head>
    <title>Video Upload</title>
</head>
<body>
    <h1>🎬 Upload a Video</h1>

    <div class="upload-section">
        <input type="file" id="videoInput" accept="video/*">
        <button onclick="uploadVideo()" id="uploadBtn">📤 Upload Video</button>
        <br><br>

        <!-- Progress Section -->
        <div id="progressSection" style="display: none;">
            <div class="progress-container">
                <progress id="progressBar" value="0" max="100"></progress>
                <span id="progressText">0%</span>
            </div>
            <div id="statusText">Preparing upload...</div>
        </div>

        <!-- Success/Error Messages -->
        <div id="messageArea"></div>
    </div>

    <style>
        .upload-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px dashed #dee2e6;
        }

        .progress-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }

        #progressBar {
            flex: 1;
            height: 20px;
            border-radius: 10px;
        }

        #progressText {
            font-weight: bold;
            color: #007bff;
            min-width: 50px;
        }

        #statusText {
            font-size: 14px;
            color: #666;
            margin: 5px 0;
        }

        #messageArea {
            margin: 15px 0;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }

        button:hover {
            background: #0056b3;
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        input[type="file"] {
            margin: 10px 0;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            max-width: 400px;
        }
    </style>

    <h2>📹 Uploaded Videos</h2>
    {% if videos %}
        <div class="video-stats">
            <p><strong>Total Videos:</strong> {{ videos|length }}</p>
        </div>
        {% for video in videos %}
            <div class="video-item">
                <div class="video-info">
                    <h3>{{ video[1] }}</h3>
                    <p class="video-details">
                        <span>📁 Size: {{ "%.2f"|format(video[2] / (1024 * 1024)) if video[2] else "Unknown" }}MB</span>
                        <span>📅 Uploaded: {{ video[3].strftime('%Y-%m-%d %H:%M') if video[3] else "Unknown" }}</span>
                        <span>🆔 ID: {{ video[0] }}</span>
                    </p>
                </div>
                <video width="320" height="240" controls preload="metadata">
                    <source src="{{ url_for('stream_video', video_id=video[0]) }}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
        {% endfor %}
    {% else %}
        <div class="no-videos">
            <p>No videos uploaded yet. Upload your first video above! 🎬</p>
        </div>
    {% endif %}

    <style>
        .video-stats {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 15px 0;
        }

        .video-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            display: flex;
            gap: 15px;
            align-items: flex-start;
        }

        .video-info {
            flex: 1;
        }

        .video-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .video-details {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }

        .video-details span {
            display: block;
            margin: 2px 0;
        }

        .no-videos {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        video {
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>

    <script>
        function uploadVideo() {
            const fileInput = document.getElementById('videoInput');
            const file = fileInput.files[0];
            const uploadBtn = document.getElementById('uploadBtn');
            const progressSection = document.getElementById('progressSection');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const statusText = document.getElementById('statusText');
            const messageArea = document.getElementById('messageArea');

            // Validation
            if (!file) {
                showMessage("Please select a video file.", "error");
                return;
            }

            // Check file type
            if (!file.type.startsWith('video/')) {
                showMessage("Please select a valid video file.", "error");
                return;
            }

            // Check file size (256MB max)
            const maxSize = 256 * 1024 * 1024; // 256MB
            if (file.size > maxSize) {
                showMessage(`File too large! Maximum size is 256MB. Your file is ${(file.size / (1024 * 1024)).toFixed(2)}MB.`, "error");
                return;
            }

            // Show file info
            const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
            showMessage(`Uploading: ${file.name} (${fileSizeMB}MB)`, "info");

            // Prepare UI for upload
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '⏳ Uploading...';
            progressSection.style.display = 'block';
            progressBar.value = 0;
            progressText.textContent = '0%';
            statusText.textContent = 'Preparing upload...';

            const xhr = new XMLHttpRequest();
            xhr.open("POST", "/upload", true);

            // Upload progress
            xhr.upload.onprogress = function (event) {
                if (event.lengthComputable) {
                    const percent = Math.round((event.loaded / event.total) * 100);
                    progressBar.value = percent;
                    progressText.textContent = percent + '%';

                    // Update status based on progress
                    if (percent < 25) {
                        statusText.textContent = 'Starting upload...';
                    } else if (percent < 50) {
                        statusText.textContent = 'Uploading to server...';
                    } else if (percent < 75) {
                        statusText.textContent = 'Processing video...';
                    } else if (percent < 100) {
                        statusText.textContent = 'Almost done...';
                    } else {
                        statusText.textContent = 'Finalizing upload...';
                    }
                }
            };

            // Upload start
            xhr.upload.onloadstart = function() {
                statusText.textContent = 'Upload started...';
                console.log('📤 Upload started for:', file.name);
            };

            // Upload complete
            xhr.onload = function () {
                if (xhr.status === 200) {
                    progressBar.value = 100;
                    progressText.textContent = '100%';
                    statusText.textContent = 'Upload completed successfully!';
                    showMessage(`✅ "${file.name}" uploaded successfully!`, "success");

                    // Reset and reload after delay
                    setTimeout(() => {
                        resetUploadForm();
                        window.location.reload();
                    }, 2000);
                } else {
                    showMessage(`❌ Upload failed: ${xhr.responseText}`, "error");
                    resetUploadForm();
                }
            };

            // Upload error
            xhr.onerror = function() {
                showMessage("❌ Network error occurred during upload.", "error");
                resetUploadForm();
            };

            // Upload timeout
            xhr.ontimeout = function() {
                showMessage("❌ Upload timed out. Please try again.", "error");
                resetUploadForm();
            };

            // Set timeout (5 minutes for large files)
            xhr.timeout = 5 * 60 * 1000;

            // Send the file
            const formData = new FormData();
            formData.append("video", file);
            xhr.send(formData);
        }

        function showMessage(message, type) {
            const messageArea = document.getElementById('messageArea');
            messageArea.textContent = message;
            messageArea.className = type;
            messageArea.style.display = 'block';

            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(() => {
                    messageArea.style.display = 'none';
                }, 5000);
            }
        }

        function resetUploadForm() {
            const uploadBtn = document.getElementById('uploadBtn');
            const progressSection = document.getElementById('progressSection');

            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '📤 Upload Video';
            progressSection.style.display = 'none';
        }

        // File input change handler
        document.getElementById('videoInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
                showMessage(`Selected: ${file.name} (${fileSizeMB}MB) - Ready to upload!`, "info");
            }
        });
    </script>
</body>
</html>
