-- <PERSON><PERSON>t to create test_videos table
-- Run this in phpMyAdmin or MySQL command line

USE giggenius;

-- Create test_videos table for video upload testing
CREATE TABLE IF NOT EXISTS test_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VA<PERSON>HA<PERSON>(255) NOT NULL,
    video_data LONGBLOB NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_uploaded_at (uploaded_at)
);

-- Optional: Add some sample data (remove this if you don't want sample data)
-- INSERT INTO test_videos (filename, video_data, file_size, mime_type) 
-- VALUES ('sample.mp4', '', 0, 'video/mp4');

-- Check if table was created successfully
DESCRIBE test_videos;

-- Show table info
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH,
    CREATE_TIME
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'giggenius' 
AND TABLE_NAME = 'test_videos';
