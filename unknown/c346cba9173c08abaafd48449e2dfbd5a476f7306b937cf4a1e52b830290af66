-- <PERSON><PERSON> Script to add profile_video column to approve_genius table
-- Run this in phpMyAdmin or MySQL command line

USE giggenius;

-- Add profile_video column to existing approve_genius table
ALTER TABLE approve_genius 
ADD COLUMN profile_video LONGBLOB NULL COMMENT 'Stores genius profile video as binary data';

-- Optional: Add index for better performance (if you plan to query by video existence)
-- ALTER TABLE approve_genius 
-- ADD INDEX idx_has_video ((CASE WHEN profile_video IS NOT NULL THEN 1 ELSE 0 END));

-- Verify the column was added
DESCRIBE approve_genius;

-- Check table structure
SHOW CREATE TABLE approve_genius;

-- Optional: Check current data
SELECT 
    id, 
    first_name, 
    last_name,
    CASE 
        WHEN profile_video IS NULL THEN 'No Video'
        ELSE CONCAT('Video Present (', LENGTH(profile_video), ' bytes)')
    END as video_status
FROM approve_genius 
LIMIT 10;
