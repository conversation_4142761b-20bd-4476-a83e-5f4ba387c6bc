-- <PERSON><PERSON> to create video_files table for file system video storage
-- Run this in phpMyAdmin or MySQL command line

USE giggenius;

-- Create video_files table
CREATE TABLE IF NOT EXISTS video_files (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Unique video file ID',
    filename VARCHAR(255) NOT NULL COMMENT 'Original filename of uploaded video',
    filepath VARCHAR(500) NOT NULL COMMENT 'Full path to video file on server',
    file_size INT NOT NULL COMMENT 'File size in bytes',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Upload timestamp',
    user_id INT COMMENT 'User ID who uploaded the video',
    video_type ENUM('profile', 'portfolio', 'other') DEFAULT 'profile' COMMENT 'Type of video',
    
    -- Indexes for better performance
    INDEX idx_user_id (user_id),
    INDEX idx_video_type (video_type),
    INDEX idx_uploaded_at (uploaded_at),
    INDEX idx_user_video_type (user_id, video_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Table for storing video file information';

-- Verify table creation
DESCRIBE video_files;

-- Show table creation statement
SHOW CREATE TABLE video_files;

-- Display current video files (will be empty initially)
SELECT 
    id,
    filename,
    CONCAT(ROUND(file_size / (1024 * 1024), 2), ' MB') as size,
    video_type,
    user_id,
    uploaded_at
FROM video_files 
ORDER BY uploaded_at DESC 
LIMIT 10;

-- Show table statistics
SELECT 
    COUNT(*) as total_videos,
    CONCAT(ROUND(SUM(file_size) / (1024 * 1024), 2), ' MB') as total_storage,
    CONCAT(ROUND(AVG(file_size) / (1024 * 1024), 2), ' MB') as avg_file_size,
    MIN(uploaded_at) as first_upload,
    MAX(uploaded_at) as latest_upload
FROM video_files;

-- Show videos by type
SELECT 
    video_type,
    COUNT(*) as count,
    CONCAT(ROUND(SUM(file_size) / (1024 * 1024), 2), ' MB') as total_size
FROM video_files 
GROUP BY video_type;

-- Show videos by user (top 10 users with most videos)
SELECT 
    user_id,
    COUNT(*) as video_count,
    CONCAT(ROUND(SUM(file_size) / (1024 * 1024), 2), ' MB') as total_size
FROM video_files 
GROUP BY user_id 
ORDER BY video_count DESC 
LIMIT 10;
