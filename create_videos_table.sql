-- <PERSON><PERSON> to create videos table for video upload system
-- Run this in phpMyAdmin or MySQL command line

USE giggenius;

-- Drop table if it exists (optional - remove this line if you want to keep existing data)
-- DROP TABLE IF EXISTS videos;

-- Create videos table with all necessary columns
CREATE TABLE IF NOT EXISTS videos (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Unique video ID',
    filename VARCHAR(255) NOT NULL COMMENT 'Original filename of uploaded video',
    video_data LONGBLOB NOT NULL COMMENT 'Binary video data',
    file_size INT NOT NULL DEFAULT 0 COMMENT 'File size in bytes',
    mime_type VARCHAR(100) DEFAULT 'video/mp4' COMMENT 'MIME type of video file',
    duration INT DEFAULT NULL COMMENT 'Video duration in seconds',
    width INT DEFAULT NULL COMMENT 'Video width in pixels',
    height INT DEFAULT NULL COMMENT 'Video height in pixels',
    uploaded_by INT DEFAULT NULL COMMENT 'User ID who uploaded the video',
    upload_ip VARCHAR(45) DEFAULT NULL COMMENT 'IP address of uploader',
    status ENUM('active', 'processing', 'deleted', 'failed') DEFAULT 'active' COMMENT 'Video status',
    views INT DEFAULT 0 COMMENT 'Number of times video was viewed',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Upload timestamp',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
    
    -- Indexes for better performance
    INDEX idx_filename (filename),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_status (status),
    INDEX idx_uploaded_at (uploaded_at),
    INDEX idx_file_size (file_size)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Table for storing uploaded videos';

-- Create a view for easy video information retrieval (without binary data)
CREATE OR REPLACE VIEW video_info AS
SELECT 
    id,
    filename,
    file_size,
    CONCAT(ROUND(file_size / (1024 * 1024), 2), ' MB') as file_size_mb,
    mime_type,
    duration,
    CASE 
        WHEN duration IS NOT NULL THEN 
            CONCAT(
                FLOOR(duration / 60), ':', 
                LPAD(duration % 60, 2, '0')
            )
        ELSE 'Unknown'
    END as duration_formatted,
    CONCAT(width, 'x', height) as resolution,
    uploaded_by,
    upload_ip,
    status,
    views,
    uploaded_at,
    updated_at,
    DATEDIFF(NOW(), uploaded_at) as days_old
FROM videos
ORDER BY uploaded_at DESC;

-- Insert some sample data (optional - remove if not needed)
-- INSERT INTO videos (filename, video_data, file_size, mime_type) 
-- VALUES ('sample.mp4', '', 0, 'video/mp4');

-- Verify table creation
DESCRIBE videos;

-- Show table creation statement
SHOW CREATE TABLE videos;

-- Show indexes
SHOW INDEX FROM videos;

-- Display current videos (will be empty initially)
SELECT 
    id,
    filename,
    CONCAT(ROUND(file_size / (1024 * 1024), 2), ' MB') as size,
    mime_type,
    status,
    uploaded_at
FROM videos 
ORDER BY uploaded_at DESC 
LIMIT 10;

-- Show table statistics
SELECT 
    COUNT(*) as total_videos,
    CONCAT(ROUND(SUM(file_size) / (1024 * 1024), 2), ' MB') as total_storage,
    CONCAT(ROUND(AVG(file_size) / (1024 * 1024), 2), ' MB') as avg_file_size,
    MIN(uploaded_at) as first_upload,
    MAX(uploaded_at) as latest_upload
FROM videos;

-- Create a stored procedure to get video statistics
DELIMITER //
CREATE PROCEDURE GetVideoStats()
BEGIN
    SELECT 
        'Total Videos' as metric, 
        COUNT(*) as value 
    FROM videos
    UNION ALL
    SELECT 
        'Total Storage (MB)', 
        ROUND(SUM(file_size) / (1024 * 1024), 2)
    FROM videos
    UNION ALL
    SELECT 
        'Average File Size (MB)', 
        ROUND(AVG(file_size) / (1024 * 1024), 2)
    FROM videos
    UNION ALL
    SELECT 
        'Total Views', 
        SUM(views)
    FROM videos
    UNION ALL
    SELECT 
        'Active Videos', 
        COUNT(*)
    FROM videos 
    WHERE status = 'active';
END //
DELIMITER ;

-- Create a function to format file sizes
DELIMITER //
CREATE FUNCTION FormatFileSize(size_bytes INT) 
RETURNS VARCHAR(20)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result VARCHAR(20);
    
    IF size_bytes >= 1073741824 THEN
        SET result = CONCAT(ROUND(size_bytes / 1073741824, 2), ' GB');
    ELSEIF size_bytes >= 1048576 THEN
        SET result = CONCAT(ROUND(size_bytes / 1048576, 2), ' MB');
    ELSEIF size_bytes >= 1024 THEN
        SET result = CONCAT(ROUND(size_bytes / 1024, 2), ' KB');
    ELSE
        SET result = CONCAT(size_bytes, ' bytes');
    END IF;
    
    RETURN result;
END //
DELIMITER ;

-- Usage examples:
-- SELECT id, filename, FormatFileSize(file_size) as formatted_size FROM videos;
-- CALL GetVideoStats();
-- SELECT * FROM video_info LIMIT 10;
