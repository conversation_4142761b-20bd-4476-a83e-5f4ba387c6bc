-- Simple SQL Script to create videos table
-- Run this in phpMyAdmin SQL tab

USE giggenius;

-- Create basic videos table
CREATE TABLE IF NOT EXISTS videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    video_data LONGBLOB NOT NULL,
    file_size INT NOT NULL DEFAULT 0,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Verify table was created
DESCRIBE videos;

-- Check if table exists and show structure
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'giggenius' 
AND TABLE_NAME = 'videos'
ORDER BY ORDINAL_POSITION;
