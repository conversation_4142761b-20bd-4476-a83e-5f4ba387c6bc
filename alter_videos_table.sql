-- <PERSON><PERSON> Script to add file_size column to existing videos table
-- Run this in phpMyAdmin or MySQL command line

USE giggenius;

-- Add file_size column to existing videos table
ALTER TABLE videos 
ADD COLUMN file_size INT NOT NULL DEFAULT 0 COMMENT 'File size in bytes';

-- Optional: Update existing records with estimated file size based on video_data length
-- (This will calculate the actual size of existing videos)
UPDATE videos 
SET file_size = LENGTH(video_data) 
WHERE file_size = 0 AND video_data IS NOT NULL;

-- Verify the column was added
DESCRIBE videos;

-- Check the updated table structure
SHOW CREATE TABLE videos;

-- Optional: View file sizes of existing videos
SELECT 
    id,
    filename,
    CASE 
        WHEN file_size > 0 THEN CONCAT(ROUND(file_size / (1024 * 1024), 2), ' MB')
        ELSE 'Unknown'
    END as file_size_mb,
    uploaded_at
FROM videos 
ORDER BY uploaded_at DESC;

-- Optional: Get total storage used
SELECT 
    COUNT(*) as total_videos,
    CONCAT(ROUND(SUM(file_size) / (1024 * 1024), 2), ' MB') as total_storage_mb,
    CONCAT(ROUND(AVG(file_size) / (1024 * 1024), 2), ' MB') as avg_file_size_mb
FROM videos 
WHERE file_size > 0;
